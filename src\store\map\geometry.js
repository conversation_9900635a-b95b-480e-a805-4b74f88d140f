import { defineStore } from "pinia";
import {
  getMapInstance,
  initGeometryLayer,
  getGeometryLayer,
  removeGeometryLayer,
  getGeometryInstance,
  setGeometryInstance,
  removeGeometryInstance,
  removeAllGeometryInstance,
} from "./utils/instance-store";

import {
  createGeometryLayer,
  createGeometry,
  defaultSymbol,
} from "./utils/geometry-util";
export const useGeometryStore = (mapId) => {
  return defineStore(`${mapId}-geometry`, {
    state: () => ({
      geometries: [],
      mapId,
    }),

    actions: {
      getGeometryLayer() {
        return getGeometryLayer();
      },
      initGeometryLayer() {
        if (this.getGeometryLayer()) return;
        const geometryLayer = createGeometryLayer();
        const { map } = getMapInstance(this.mapId);
        map.add(geometryLayer);
        initGeometryLayer(geometryLayer);
      },
      deleteGeometryLayer() {
        const geometryLayer = getGeometryLayer();
        if (geometryLayer) {
          const { map } = getMapInstance(this.mapId);
          geometryLayer.graphics.removeAll();
          removeAllGeometryInstance();
          geometryLayer.destroy();
          map.remove(geometryLayer);
          removeGeometryLayer();
        }
      },
      getGeometryInstance(featureId) {
        return getGeometryInstance(featureId);
      },
      addGeometry({ feature, featureId = "OBJECTID", options } = {}) {
        if (!this.getGeometryLayer()) this.initGeometryLayer();
        const geometryLayer = getGeometryLayer();
        const features = Array.isArray(feature) ? feature : [feature];
        features.forEach(({ geometry, attributes }) => {
          const fid = attributes[featureId];
          if (!this.getGeometryInstance(fid)) {
            const geometryInstance = createGeometry({
              featureId: fid,
              geometry,
              attributes,
              options,
              symbol: options.symbol || defaultSymbol,
            });
            setGeometryInstance({ geometry: geometryInstance, featureId: fid });
            geometryLayer.graphics.add(geometryInstance);
            this.geometries.push({
              featureId: fid,
              geometry,
              attributes,
            });
            if (options.isFitbounds !== false)
              this.locateGeoSceneFeatureById(fid);
          }
        });
      },
      locateGeoSceneFeatureById(featureId) {
        const geometryInstance = this.getGeometryInstance(featureId);
        if (geometryInstance) {
          const { view } = getMapInstance(this.mapId);
          const { extent, type } = geometryInstance.geometry;
          if (type === "point" && !extent) {
            view.goTo(
              { target: geometryInstance.geometry, zoom: 16 },
              {
                duration: 1000,
              }
            );
          } else {
            extent && view.goTo(extent, {});
          }
        }
      },
      updateGeometrySymbol(featureId, symbol) {
        const geometryInstance = this.getGeometryInstance(featureId);
        if (geometryInstance) {
          geometryInstance.symbol = symbol;
        }
      },
      removeGeometry(featureId) {
        const featureIds = Array.isArray(featureId) ? featureId : [featureId];
        const geometryLayer = getGeometryLayer();
        featureIds.forEach((featureId) => {
          const geometryInstance = this.getGeometryInstance(featureId);
          if (geometryInstance) {
            geometryLayer.graphics.remove(geometryInstance);
            removeGeometryInstance(featureId);
            this.geometries = this.geometries.filter(
              (geometry) => geometry.featureId !== featureId
            );
          }
        });
      },
      removeAllGeometry() {
        const geometryLayer = getGeometryLayer();
        geometryLayer.graphics.removeAll();
        removeAllGeometryInstance();
        this.geometries = [];
      },
    },
  });
};
