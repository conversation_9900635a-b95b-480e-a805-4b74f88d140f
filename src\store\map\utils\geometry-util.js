import GraphicsLayer from "@geoscene/core/layers/GraphicsLayer";
import Graphic from "@geoscene/core/Graphic";
import Point from "@geoscene/core/geometry/Point.js";

export const createGeometryLayer = (id = "geometry-layer") => {
  return new GraphicsLayer(id);
};

export const createGeometry = ({
  featureId,
  geometry,
  symbol,
  attributes,
  options,
} = {}) => {
  return new Graphic({
    geometry,
    symbol,
    attributes,
    id: attributes ? attributes[featureId] : featureId,
    options,
  });
};

export const createPointGeometry = ({ x, y, spatialReference } = {}) => {
  return new Point({
    type: "point",
    x,
    y,
    ...(spatialReference ? { spatialReference } : {}),
  });
};
export const defaultSymbol = {
  type: "simple-fill",
  color: "rgba(54,123,245,0.15)",
  outline: {
    color: "rgba(30,120,233,0.6)",
    width: 1,
    type: "simple-line",
  },
};

export const linkFeatureSymbol = {
  type: "simple-fill",
  color: "rgba(255,25,25,0.2)",
  outline: {
    color: "rgba(255,25,25,1)",
    width: 2,
    type: "simple-line",
  },
};

export const linkNoFeatureSymbol = {
  type: "simple-marker",
  color: "rgba(244,0,4,1)",
  size: "24px",
  outline: {
    color: "rgba(255,25,25,1)",
    width: 0,
    type: "simple-line",
  },
};

export const locatePolygonSymbol = {
  type: "simple-fill",
  color: "rgba(54, 123, 245, 0.4)",
  outline: {
    color: "rgba(54, 123, 245, 0.8)",
    width: 2,
    type: "simple-line",
  },
};
export const locatePointSymbol = {
  type: "simple-marker",
  color: "rgba(240, 18, 18, 1)",
  size: "24px",
  outline: {
    color: "rgba(255,25,25,1)",
    width: 1,
    type: "simple-line",
  },
};
