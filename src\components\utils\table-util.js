import { getTextWidth } from "@/utils";
import { getFormItemEnumOptions } from "@/components/utils/form-util";
import { h } from "vue";
export const shzt_map = [
  {
    title: "待审核",
    value: "0",
    className: "shzt-pending",
  },
  {
    title: "已审核",
    value: "1",
    className: "shzt-approved",
  },
  {
    title: "审核不通过",
    value: "-1",
    className: "shzt-rejected",
  },
  {
    title: "未提交审核",
    value: "999",
    className: "shzt-unsubmitted",
  },
  {
    title: "未录入",
    value: "",
    className: "shzt-empty",
  },
];

const rwzt_map = [
  {
    title: "终结",
    value: "0",
    className: "rwzt-end",
  },
  {
    title: "进行中",
    value: "1",
    className: "rwzt-processing",
  },
  {
    title: "未开始",
    value: "999",
    className: "rwzt-not-start",
  },
];
export const generateTableColumns = (fields, linkField) => {
  const stzt = fields.find((item) => item.field && item.field === linkField);
  if (stzt)
    fields.unshift({
      field: "_stzt",
      alias: "上图状态",
    });
  return fields.map((item) => {
    let prop = item.field || item.key;
    let label = item.alias || item.title;
    let column = {
      prop,
      label,
    };
    if (prop === "_stzt") {
      column.formatter = (row) => {
        return h(
          "span",
          {
            class:
              row[prop] == "1"
                ? "tz-stzt tz-stzt-success"
                : "tz-stzt tz-stzt-empty",
          },
          row[prop] == "1" ? "已上图" : "未上图"
        );
      };
    }
    if (prop === "_shzt") {
      column.formatter = (row) => {
        const value = row[prop];
        const shztItem = shzt_map.find((m) => m.value === value) || shzt_map[4];
        return h(
          "span",
          {
            class: `shzt-tag ${shztItem.className}`,
          },
          shztItem.title
        );
      };
    } else if (prop === "_rwzt") {
      column.formatter = (row) => {
        const value = row[prop];
        const rwztItem = rwzt_map.find((m) => m.value === value) || rwzt_map[2];
        return h(
          "span",
          {
            class: `rwzt-tag ${rwztItem.className}`,
          },
          rwztItem.title
        );
      };
    } else if (item.type === "enum") {
      const enumOptions = getFormItemEnumOptions(item);
      column.formatter = (row) => {
        return enumOptions.find((p) => p.value === row[prop])?.label || "-";
      };
    } else if (item.type === "boolean") {
      column.formatter = (row) => {
        return row[prop] == "1" ? "是" : "否";
      };
    }

    return column;
  });
};

export const setTableColumnWidth = (columns, tableData) => {
  if (tableData?.length === 0) return columns;
  columns.forEach((column) => {
    let maxWidth = 300;
    let realLength = getTextWidth("14px", String(column.label)) + 2;
    tableData.forEach((item) => {
      let ml = getTextWidth("14px", String(item[column.prop])) + 2;
      realLength = Math.max(realLength, ml);
    });
    column.width = realLength + 24;
    if (column.width >= maxWidth) {
      column.width = maxWidth;
      column.showOverflowTooltip = true;
    }
    if (column.prop === "_shzt") {
      column.width = 120;
    }
  });
  return columns;
};
