<script setup>
import {
  ref,
  onMounted,
  onBeforeMount,
  reactive,
  getCurrentInstance,
} from "vue";
import { getTzFieldTableData, getTzData, deleteTzData } from "@/api";
import { ElMessage, ElMessageBox } from "element-plus";
import TzSearchForm from "@/components/tzdatamanage/tz-search-form.vue";
import UpdateTzData from "./update-tz-data.vue";
import linkFeatureData from "./link-feature-data.vue";
import {
  generateTableColumns,
  setTableColumnWidth,
} from "@/components/utils/table-util";
import { useDynamicZxid } from "@/components/utils/dynamic-util";
import { useZxDataHooks } from "@/components/hooks/zx-data-hooks";
const {
  getExistConfig,
  handleMapMounted,
  linkLayerData,
  hightlightGeometryByTz,
  removeCurrentGeometry,
  mapShow,
  expandRight,
} = useZxDataHooks({ mapId: "tz-data-manage" });
const basicCheckItemCommonRef = ref(null);
const dynamicSearchRef = ref(null);
const tableRef = ref(null);
const rootRef = ref(null);
const headerRef = ref(null);
const instance = getCurrentInstance();
const tableState = reactive({
  tableData: [],
  tableHeight: 0,
  tableColumns: [],
  fields: [],
  pageInfo: {
    index: 1,
    size: 10,
  },
  total: 0,
  currentRowData: {},
});

const dialogState = reactive({
  title: "新增台账",
  visible: false,
  width: 800,
  modalClass: "fangwuzxjc-dialog-modal",
  editFormState: {},
});

const { zxId } = useDynamicZxid();
onBeforeMount(() => {
  initData();
});

onMounted(() => {
  setTimeout(() => {
    setComponentHeight();
  }, 500);
  instance.proxy?.$eventBus?.on("is-link-feature-data", (value) => {
    if (value) {
      removeCurrentGeometry();
    }
  });
});
const initData = async () => {
  await getExistConfig();
  await getTableColumns();
  await getTableData();
};
const setComponentHeight = () => {
  basicCheckItemCommonRef.value.style.height =
    rootRef.value.clientHeight - headerRef.value.clientHeight - 16 + "px";
  tableState.tableHeight = basicCheckItemCommonRef.value.clientHeight - 102;
};
const getTableColumns = async () => {
  let params = {
    zid: zxId.value,
  };
  const res = await getTzFieldTableData(params);
  if (res.code === 200) {
    tableState.fields = res.data;
    tableState.tableColumns = generateTableColumns(
      res.data,
      linkLayerData.value.tzzd
    );
  }
};
const getTableData = async (queryKvs = []) => {
  let params = {
    zid: zxId.value,
    queryKvs,
    pageInfo: tableState.pageInfo,
  };
  const res = await getTzData(params);
  tableState.currentRowData = {};
  if (res.code === 200) {
    tableState.tableColumns = setTableColumnWidth(
      tableState.tableColumns,
      res.data.list
    );
    tableState.tableData = res.data.list;
    tableState.total = res.data.total;
  }
};
const handleReset = () => {
  tableState.pageInfo.index = 1;
  tableState.pageInfo.size = 10;
  dynamicSearchRef.value.reset();
  getTableData();
};
const handleSearch = () => {
  let params = dynamicSearchRef.value.getQueryParams();
  getTableData(params);
};
const handleAdd = () => {
  dialogState.visible = true;
  dialogState.title = "新增台账";
};
const closeDialog = () => {
  dialogState.visible = false;
  dialogState.editFormState = {};
};
const handleSaveSuccess = () => {
  getTableData();
  dialogState.visible = false;
  dialogState.editFormState = {};
};
const handleEdit = (index, row) => {
  dialogState.visible = true;
  dialogState.title = "编辑台账";
  dialogState.editFormState = row;
};
const handleDelete = (index, row) => {
  ElMessageBox.confirm("确定要删除该台账吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await deleteTzData({ ids: row.id });
      if (res.code === 200) {
        getTableData();
      } else {
        ElMessage.warning(res.message || "删除失败");
      }
    })
    .catch(() => {});
};
const batchDelete = () => {
  const selectedRows = tableRef.value.getSelectionRows();
  if (selectedRows.length === 0) {
    $ElMessage.warning("请选择要删除的台账");
    return;
  }
  ElMessageBox.confirm("确定要删除所选台账吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await deleteTzData({
        ids: selectedRows.map((item) => item.id).join(","),
      });
      if (res.code === 200) {
        getTableData();
      } else {
        ElMessage.warning(res.message || "删除失败");
      }
    })
    .catch(() => {});
};
const handleSizeChange = (size) => {
  tableState.pageInfo.size = size;
  getTableData();
};
const handleCurrentChange = (index) => {
  tableState.pageInfo.index = index;
  getTableData();
};

const clickRowData = (row) => {
  hightlightGeometryByTz(row);
  tableState.currentRowData = row;
};
</script>

<template>
  <div class="basic-check-item-common flex-left-right" ref="rootRef">
    <div class="basic-check-item-common-left">
      <div class="basic-check-item-common-header" ref="headerRef">
        <div class="basic-check-item-common-header-title">
          <span class="line"></span>
          <span class="title">筛选条件</span>
        </div>
        <TzSearchForm :fields="tableState.fields" ref="dynamicSearchRef">
          <template #search-btn>
            <el-button type="primary" icon="Search" @click="handleSearch"
              >查询</el-button
            >
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
            <el-button icon="Plus" @click="handleAdd">新增</el-button>
            <el-button icon="Delete" type="danger" @click="batchDelete"
              >批量删除</el-button
            >
          </template>
        </TzSearchForm>
      </div>
      <div
        class="basic-check-item-common-content"
        ref="basicCheckItemCommonRef"
      >
        <div class="basic-check-item-common-header-title">
          <span class="line"></span>
          <span class="title">台账列表 </span>
        </div>
        <div class="basic-check-item-common-content-table">
          <el-table
            :data="tableState.tableData"
            :height="tableState.tableHeight"
            ref="tableRef"
            tooltip-effect="light"
            @row-click="clickRowData"
            class="highlight-table"
            highlight-current-row
            :tooltip-options="{
              popperClass: 'fwaq-zx-table-tooltip',
            }"
          >
            <el-table-column type="index" width="55" label="序号" />
            <el-table-column type="selection" width="55" />
            <el-table-column
              v-for="item in tableState.tableColumns"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :width="item.width"
              :min-width="item.minWidth"
              :formatter="item.formatter"
              :show-overflow-tooltip="item.showOverflowTooltip"
            />
            <el-table-column
              label="操作"
              width="160"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  @click="handleEdit(scope.$index, scope.row)"
                  >编辑</el-button
                >
                <el-button
                  size="small"
                  type="danger"
                  @click="handleDelete(scope.$index, scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="basic-check-item-common-content-pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 30, 40, 50]"
            v-model:current-page="tableState.pageInfo.index"
            v-model:page-size="tableState.pageInfo.size"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :total="tableState.total"
          />
        </div>
      </div>
    </div>
    <div
      class="basic-check-item-common-right"
      :class="expandRight ? 'expand' : 'shrink'"
      v-resize="{ minResize: 30, directions: ['left'] }"
    >
      <ExpandShrink
        @on-collapsed-change="expandRight = !expandRight"
      ></ExpandShrink>
      <GeoSceneMap
        @on-map-mounted="handleMapMounted"
        map-id="tz-data-manage"
        v-if="linkLayerData"
        v-bind="$attrs"
      >
        <linkFeatureData
          :linkLayerData="linkLayerData"
          :currentRowData="tableState.currentRowData"
          v-if="mapShow"
          mapId="tz-data-manage"
          @link-success="getTableData()"
        ></linkFeatureData>
      </GeoSceneMap>
    </div>
  </div>

  <el-dialog
    :close-on-click-modal="false"
    v-model="dialogState.visible"
    :show-close="false"
    :modal-class="dialogState.modalClass"
    :width="dialogState.width"
    class="fangwuzxjc-dialog"
  >
    <template #header>
      <div class="my-header">
        <h4>{{ dialogState.title }}</h4>
        <el-icon class="el-icon--left" @click="closeDialog">
          <CircleCloseFilled />
        </el-icon>
      </div>
    </template>
    <div class="fangwuzxjc-dialog-content">
      <UpdateTzData
        :fields="tableState.fields"
        :zxId="zxId"
        :editFormState="dialogState.editFormState"
        ref="updateTzDataRef"
        @cancel="closeDialog"
        @save-success="handleSaveSuccess"
        v-if="dialogState.visible"
      />
    </div>
  </el-dialog>
</template>
