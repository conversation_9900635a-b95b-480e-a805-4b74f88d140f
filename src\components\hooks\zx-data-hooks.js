import {
  ref,
  onMounted,
  onBeforeMount,
  reactive,
  getCurrentInstance,
  onBeforeUnmount,
} from "vue";
import {
  getZxDataColumns,
  getZxData,
  getZxJcbPcList,
  getZxLinkLayerConfig,
  featureQuery,
  getTzLayerWkt,
} from "@/api";
import DynamicSearchForm from "@/components/dymamicsearchform/index.vue";
import ZxDataDetail from "@/components/zxdatadetail/index.vue";
import {
  useDynamicForm,
  useDynamicZxid,
} from "@/components/utils/dynamic-util";
import {
  generateTableColumns,
  setTableColumnWidth,
  shzt_map,
} from "@/components/utils/table-util";
import { useMapStoreHooks } from "@/components/hooks/map-store-hooks";
import { getArcgisWhere, getArcgisValue } from "@/components/utils/arcgis-util";
import {
  locatePolygonSymbol,
  locatePointSymbol,
  createPointGeometry,
} from "@/store/map/utils/geometry-util";

export function useZxDataHooks(options = {}) {
  const {
    addGeoSceneLayer,
    removeAllGeoSceneLayer,
    addGeometry,
    removeGeometry,
  } = useMapStoreHooks(options.mapId);

  const { pageType = "lr" } = options;
  const { watchFormId, formId } = useDynamicForm();
  const { zxId } = useDynamicZxid();
  const { proxy } = getCurrentInstance();
  const basicCheckItemCommonRef = ref(null);
  const dynamicSearchRef = ref(null);
  const tableRef = ref(null);
  const rootRef = ref(null);
  const headerRef = ref(null);
  const mapShow = ref(false);
  const expandRight = ref(true);
  const linkLayerData = ref(null);
  const currentHightId = ref("");

  const tableState = reactive({
    tableData: [],
    tableHeight: 0,
    tableColumns: [],
    fields: [],
    pageInfo: {
      index: 1,
      size: 10,
    },
    total: 0,
  });
  const zxDataDetailState = reactive({
    visible: false,
    rowData: {},
  });
  const getShztList = () => {
    if (pageType === "lr") {
      return shzt_map.filter((item) => item.value);
    }
    if (pageType === "sh") {
      return shzt_map.filter((item) => item.value && item.value !== "999");
    }
  };
  const searchState = reactive({
    _pcmc: null,
    pcmcList: [],
    _shzt: null,
    shztList: getShztList(),
  });

  const setComponentHeight = () => {
    if (basicCheckItemCommonRef.value?.style) {
      basicCheckItemCommonRef.value.style.height =
        rootRef.value.clientHeight - headerRef.value.clientHeight - 16 + "px";
      tableState.tableHeight = basicCheckItemCommonRef.value.clientHeight - 102;
    }
  };
  const getExistConfig = async () => {
    const res = await getZxLinkLayerConfig({ zxid: zxId.value });
    if (res.code == 200 && res.data) {
      linkLayerData.value = res.data;
    }
  };
  const setLinkMapData = () => {
    removeAllGeoSceneLayer();
    if (linkLayerData.value.fwcxdz) {
      addGeoSceneLayer({
        url: linkLayerData.value.fwcxdz,
        type: linkLayerData.value.fwlx,
        name: linkLayerData.value.fwmc,
        title: linkLayerData.value.fwbm || linkLayerData.value.fwmc,
        options: {
          fields: linkLayerData.value.tczdpz,
          isFitBounds: true,
        },
      });
    }
  };
  const getPcmcList = async () => {
    const res = await getZxJcbPcList({ zxid: zxId.value, jcbid: formId.value });
    if (res.code === 200) {
      searchState.pcmcList = res.data;
    }
  };
  const getTableColumns = async () => {
    let params = {
      formId: formId.value,
      zid: zxId.value,
    };
    const res = await getZxDataColumns(params);
    if (res.code === 200) {
      tableState.fields = res.data;
      tableState.tableColumns = generateTableColumns(res.data);
    }
  };
  const getTableData = async ({
    tyQueryKvs = [],
    jcxQueryKvs = [],
    tzQueryKvs = [],
  } = {}) => {
    let params = {
      jcbid: formId.value,
      zid: zxId.value,
      pageType,
      pageInfo: tableState.pageInfo,
      tyQueryKvs,
      jcxQueryKvs,
      tzQueryKvs,
    };
    const res = await getZxData(params);
    if (res.code === 200 && res.data.list) {
      tableState.tableColumns = setTableColumnWidth(
        tableState.tableColumns,
        res.data.list
      );
      tableState.tableData = res.data.list;
      tableState.total = res.data.total;
    }
  };
  const initData = async () => {
    await getPcmcList();
    await getTableColumns();
    await getTableData();
    await dynamicSearchRef.value?.initSearchFields?.();
    setComponentHeight();
  };
  const getTySearchParams = () => {
    let tyQueryKvs = [];
    if (searchState._pcmc) {
      tyQueryKvs.push({
        key: "_pcmc",
        value: searchState._pcmc,
        querytype: "like",
      });
    }
    if (searchState._shzt) {
      tyQueryKvs.push({
        key: "_shzt",
        value: searchState._shzt,
        querytype: "=",
      });
    }
    return tyQueryKvs;
  };
  const handleReset = () => {
    tableState.pageInfo.index = 1;
    tableState.pageInfo.size = 10;
    dynamicSearchRef.value.reset();
    searchState._pcmc = null;
    searchState._shzt = null;
    getTableData();
  };
  const handleSearch = () => {
    let params = dynamicSearchRef.value.getQueryParams();
    params.tyQueryKvs = getTySearchParams();
    getTableData(params);
  };
  const handleSizeChange = (size) => {
    tableState.pageInfo.size = size;
    handleSearch();
  };
  const handleCurrentChange = (index) => {
    tableState.pageInfo.index = index;
    handleSearch();
  };
  const getTitle = () => {
    let title = "录入";
    if (pageType === "sh") {
      title = "审核";
    }
    if (pageType === "ck") {
      title = "查看";
    }
    return title;
  };
  const handleView = (index, row) => {
    proxy.$eventBus.emit(
      "set-active-content-title",
      `${row._pcmc} > ${getTitle()}`
    );
    zxDataDetailState.visible = true;
    zxDataDetailState.rowData = row;
  };
  const handleBack = () => {
    zxDataDetailState.visible = false;
    zxDataDetailState.rowData = {};
    proxy.$eventBus.emit("set-active-content-title", getTitle());
    initData();
  };

  const removeCurrentGeometry = () => {
    if (currentHightId.value) {
      removeGeometry(currentHightId.value);
      currentHightId.value = "";
    }
  };
  const hightlightGeometryByWkt = async (row) => {
    const res = await getTzLayerWkt({ tzid: row.id });
    if (res.code === 200 && res.data) {
      const matches = res.data.match(/POINT\s*\(([^]+)\)/i);
      const coords = matches[1].split(",");
      currentHightId.value = "link-wkt-id";
      addGeometry({
        feature: {
          geometry: createPointGeometry({
            x: parseFloat(coords[0]),
            y: parseFloat(coords[1]),
          }),
          attributes: {
            [linkLayerData.value.tczd]: currentHightId.value,
          },
        },
        featureId: linkLayerData.value.tczd,
        options: {
          symbol: locatePointSymbol,
        },
      });
    }
  };
  const hightlightGeometryByTz = async (row) => {
    removeCurrentGeometry();
    if (linkLayerData.value.tzzd) {
      const linkValue =
        row[linkLayerData.value.tzzd] || row[`tz.${linkLayerData.value.tzzd}`];
      const zdlx = linkLayerData.value.tczdpz.find(
        (item) => item.zdmc === linkLayerData.value.tczd
      ).zdlx;
      if (linkValue) {
        const res = await featureQuery({
          url: linkLayerData.value.fwcxdz,
          where: getArcgisWhere({
            zdlx,
            key: linkLayerData.value.tczd,
            value: linkValue,
          }),
        });
        if (res?.features?.length > 0) {
          currentHightId.value = getArcgisValue({ zdlx, value: linkValue });
          addGeometry({
            feature: res.features[0],
            featureId: linkLayerData.value.tczd,
            options: {
              symbol: locatePolygonSymbol,
            },
          });
        } else {
          hightlightGeometryByWkt(row);
        }
      } else {
        hightlightGeometryByWkt(row);
      }
    }
  };
  const handleMapMounted = () => {
    setLinkMapData();
    mapShow.value = true;
  };
  const setupLifecycle = () => {
    onBeforeMount(() => {
      getExistConfig();
      watchFormId(() => {
        initData();
      });
    });
    onMounted(() => {
      proxy?.$eventBus?.on("close-zx-data-detail", () => {
        initData();
        zxDataDetailState.visible = false;
        zxDataDetailState.rowData = {};
      });
      setTimeout(() => {
        setComponentHeight();
      }, 500);
    });
    onBeforeUnmount(() => {
      proxy?.$eventBus?.off("close-zx-data-detail");
    });
  };
  return {
    setupLifecycle,
    handleReset,
    handleSearch,
    handleSizeChange,
    handleCurrentChange,
    handleView,
    handleBack,
    getExistConfig,
    setLinkMapData,
    hightlightGeometryByTz,
    removeCurrentGeometry,
    linkLayerData,
    tableState,
    basicCheckItemCommonRef,
    tableRef,
    rootRef,
    headerRef,
    dynamicSearchRef,
    zxDataDetailState,
    formId,
    zxId,
    searchState,
    DynamicSearchForm,
    ZxDataDetail,
    handleMapMounted,
    mapShow,
    expandRight,
  };
}
