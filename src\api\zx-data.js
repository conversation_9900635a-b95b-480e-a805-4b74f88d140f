import axios from "axios";

// 根据zxid和检查表id获取表头
export const getZxDataColumns = async (data) => {
  try {
    const res = await axios.get("/fwaq/tzdata/getdatacolumns", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 根据zxid和检查表id获取表数据
export const getZxData = async (data) => {
  try {
    const res = await axios.post("/fwaq/tzdata/getjcrwtz", data);
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 根据zxid以及检查表id获取批次列表
export const getZxJcbPcList = async (data) => {
  try {
    const res = await axios.get("/fwaq/jcpc/querybyxfzt", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 填充表单数据
export const setZxFormData = async (data) => {
  try {
    const res = await axios.post("/fwaq/jcz/save", data);
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 根据任务id获取表单数据

export const getZxFormData = async (data) => {
  try {
    const res = await axios.get("/fwaq/jcz/query", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 提交审核
export const submitAudit = async (data) => {
  try {
    const res = await axios.post("/fwaq/jcsh/tj", data);
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};
//通过任务id获取审核记录
export const getZxShRecord = async (data) => {
  try {
    const res = await axios.get("/fwaq/jcsh/querybyrwid", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 审核
export const zxDataSh = async (data) => {
  try {
    const res = await axios.post("/fwaq/jcsh/sh", data);
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 通过专项id和检查表id获取查询字段
export const getDynamicSearchFields = async (data) => {
  try {
    const res = await axios.get("/fwaq/zxgltzjg/getqueryfield", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 获取操作日志

export const getZxDataLog = async (data) => {
  try {
    const res = await axios.get("/fwaq/czrz/query", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 获取专项列表

export const getZxList = async () => {
  try {
    const res = await axios.get("/fwaq/zxgl/getall");
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 专项关联图层
export const zxLinkLayer = async (data) => {
  try {
    const res = await axios.post("/fwaq/zxtcpz/save", data);
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 通过专项id获取图层配置
export const getZxLinkLayerConfig = async (data) => {
  try {
    const res = await axios.get("/fwaq/zxtcpz/querybyzxid", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 通过rowid关联图层和台账的值
export const linkTzLayerValue = async (data) => {
  try {
    const res = await axios.post("/fwaq/tzdata/updatefieldvalue", data);
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};

// 通过rowid获取关联的图形wkt
export const getTzLayerWkt = async (data) => {
  try {
    const res = await axios.get("/fwaq/tzkjxx/querywktbytzid", {
      params: data,
    });
    return res?.data;
  } catch (error) {
    console.log(error);
    return error;
  }
};
