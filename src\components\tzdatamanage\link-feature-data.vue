<script setup>
import { ref, computed, getCurrentInstance, onMounted, reactive } from "vue";
import { useIdentifyFeatureHooks } from "@/components/hooks/identify-feature-hook.js";
import { useMapStoreHooks } from "@/components/hooks/map-store-hooks";
import { linkTzLayerValue } from "@/api";
import { ElMessage } from "element-plus";
import {
  linkFeatureSymbol,
  linkNoFeatureSymbol,
} from "@/store/map/utils/geometry-util";

const emits = defineEmits(["link-success"]);
const nodataVisible = ref(false);
const popupContentList = ref([]);
const isLinking = ref(false);
const currentHightlightFeature = ref("");
const instance = getCurrentInstance();
const linkPopupContent = ref(null);
const linkPopupNodataContent = ref(null);

const linkParams = reactive({
  wkt: "",
  field: "",
  value: "",
});

const props = defineProps({
  linkLayerData: {
    type: Object,
    default: () => null,
  },
  currentRowData: {
    type: Object,
    default: () => ({}),
  },
  mapId: {
    type: String,
    default: "tz-data-manage",
  },
});

const { addGeometry, removeGeometry } = useMapStoreHooks(props.mapId);

const { popupManage, popupContentVisible, initControls, queryFeautre } =
  useIdentifyFeatureHooks({
    mapId: props.mapId,
  });

const closePopup = () => {
  popupContentVisible.value = false;
  nodataVisible.value = false;
  if (currentHightlightFeature.value) {
    removeGeometry(currentHightlightFeature.value);
    currentHightlightFeature.value = "";
  }
  popupManage.value.closePopup();
};

const showPopupContent = async ({ graphic, mapEvent }) => {
  if (!isLinking.value) return;
  closePopup();
  const { tczd, fwcxdz: url } = props.linkLayerData;
  let popContent = null;
  const res = await queryFeautre({
    url,
    geometry: mapEvent.mapPoint,
    name: currentLinkValue.value,
  });
  if (res.feature) {
    const { attributes, geometry } = res.feature;
    currentHightlightFeature.value = attributes[tczd];
    linkParams.value = attributes[tczd];
    linkParams.wkt = `Point(${geometry.centroid.x},${geometry.centroid.y})`;
    addGeometry({
      feature: res.feature,
      featureId: tczd,
      options: {
        symbol: linkFeatureSymbol,
      },
    });
    popupContentList.value.forEach((p) => {
      p.value = attributes[p.zdmc] || "-";
    });
    popupContentVisible.value = true;
    popContent = linkPopupContent.value;
  } else {
    currentHightlightFeature.value = "no-data-id";
    linkParams.value = "";
    linkParams.wkt = `Point(${mapEvent.mapPoint.x},${mapEvent.mapPoint.y})`;
    nodataVisible.value = true;
    popContent = linkPopupNodataContent.value;
    addGeometry({
      feature: {
        geometry: mapEvent.mapPoint,
        attributes: { [tczd]: currentHightlightFeature.value },
      },
      options: {
        symbol: linkNoFeatureSymbol,
      },
    });
  }
  popupManage.value.openPopup({
    title: "点选结果",
    location: mapEvent.mapPoint,
    content: popContent,
  });
};

const selectLink = () => {
  $ElMessageBox
    .confirm(`是否关联当前图形？`, "提示", {
      confirmButtonText: "确 认",
      cancelButtonText: "取 消",
      closeOnClickModal: false,
      type: "warning",
    })
    .then(async () => {
      const res = await linkTzLayerValue({
        ...linkParams,
        rowid: props.currentRowData.id,
      });
      if (res.code == 200) {
        ElMessage.success("关联成功");
        emits("link-success");
        linkChange();
      } else {
        ElMessage.error(res.message || "关联失败");
      }
    });
};

const setMapCursor = (value = "default") => {
  const $elMap = document.getElementById(props.mapId);
  if ($elMap) $elMap.style.cursor = value;
};
const linkChange = () => {
  if (!props.currentRowData.id) {
    $ElMessage.warning("请单击表格行选择关联房屋信息");
    return;
  }
  isLinking.value = !isLinking.value;
  instance.proxy?.$eventBus?.emit("is-link-feature-data", isLinking.value);
  if (!isLinking.value) {
    closePopup();
    setMapCursor();
  } else {
    setMapCursor("crosshair");
    initControls({
      showPopupContent,
      closePopup,
    });
  }
};

const initPoupContentList = () => {
  if (!props.linkLayerData) return;
  linkParams.field = props.linkLayerData.tzzd;
  popupContentList.value = props.linkLayerData.tczdpz
    .filter((m) => m.sfzs)
    .map((p) => {
      return {
        ...p,
        value: "-",
      };
    });
};
const currentLinkValue = computed(() => {
  return props?.currentRowData[props?.linkLayerData?.tzzd];
});

onMounted(() => {
  initPoupContentList();
});
</script>
<template>
  <div
    class="link-popup-content"
    ref="linkPopupContent"
    v-show="popupContentVisible"
  >
    <div class="content">
      <div
        v-for="(item, index) in popupContentList"
        class="popup-item"
        :key="index"
      >
        <div class="popup-item-title">{{ item.zdbm }}：</div>
        <div class="popup-item-value" :title="item.value">{{ item.value }}</div>
      </div>
    </div>
    <div class="operation-btn">
      <el-button type="primary" @click="selectLink()" size="small"
        >选定</el-button
      >
    </div>
  </div>
  <div
    class="link-popup-content-no-data"
    ref="linkPopupNodataContent"
    v-show="nodataVisible"
  >
    <div class="title">关联要素</div>
    <div class="content">未查找到楼栋数据</div>
    <div class="operation-btn">
      <el-button type="primary" @click="selectLink()" size="small"
        >选定</el-button
      >
    </div>
  </div>
  <div class="link-content-button-box">
    <div class="link-content-button">
      <div class="link-fwdata">
        <span class="title">{{
          currentLinkValue ? "当前选中:" : "提示:"
        }}</span>
        <span class="value">{{
          currentLinkValue || "请单击表格行选择房屋"
        }}</span>
      </div>
      <el-button
        :type="isLinking ? 'primary' : 'warning'"
        @click="linkChange"
        size="small"
        >{{ isLinking ? "结束" : "上图" }}</el-button
      >
    </div>
  </div>
</template>

<style lang="less">
.link-content-button-box {
  background-color: #f3f4f6;
  padding: 6px 8px;
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  .link-content-button {
    display: flex;
    gap: 8px;
    align-items: center;
    background-color: #3b82f61a;
    padding: 8px 12px;
    border-radius: 8px;
    height: 38px;
    line-height: 22px;
    color: #111827;
    .link-fwdata {
      font-size: 12px;
      display: flex;
      gap: 6px;
      align-items: center;
      line-height: 24px;
      .title {
        color: #1c62ff;
      }
      .value {
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}

.link-popup-content {
  padding: 0 8px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  .content {
    display: flex;
    gap: 6px;
    max-height: 250px;
    flex-direction: column;
    overflow-y: auto;
    .popup-item {
      display: flex;
      line-height: 28px;
      gap: 4px;
      .popup-item-title {
        color: #1c62ff;
        font-size: 12px;
        font-weight: 500;
        max-width: 120px;
      }
      .popup-item-value {
        font-weight: 500;
        color: #000;
        font-size: 12px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .operation-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding-bottom: 4px;
  }
}
.link-popup-content-no-data {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  .title {
    font-size: 14px;
    font-weight: 500;
  }
  .content {
    font-size: 12px;
  }
  .operation-btn {
    display: flex;
    height: 40px;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    gap: 8px;
  }
}
</style>
