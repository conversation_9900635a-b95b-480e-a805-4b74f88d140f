import { defineStore } from "pinia";
import Map from "@geoscene/core/Map";
import MapView from "@geoscene/core/views/MapView";
import geosceneConfig from "@geoscene/core/config.js";
// import SpatialReference from "@geoscene/core/geometry/SpatialReference";
import { createPointGeometry } from "./utils/geometry-util";
geosceneConfig.assetsPath = "./assets";
import {
  setMapInstance,
  getMapInstance,
  setLayerInstance,
  removeMapInstance,
} from "./utils/instance-store";
import { createLayer } from "./utils/layer-util";
export const createMapStore = (mapId = "map") => {
  return defineStore(mapId, {
    state: () => ({
      mapId: mapId,
      mapState: {
        zoom: 10,
        maxZoom: 22,
        minZoom: 0,
        rotationEnabled: false,
        mapId: mapId,
        wkid: null,
        center: null,
      },
    }),
    actions: {
      getMapInstance() {
        return getMapInstance(this.mapId);
      },
      setMapState({ zoom, maxZoom, minZoom, rotationEnabled, center }) {
        this.mapState = {
          zoom,
          maxZoom,
          minZoom,
          rotationEnabled,
          ...(center && { center }),
        };
      },
      initMap(
        {
          zoom = 10,
          maxZoom = 22,
          minZoom = 0,
          rotationEnabled = false,
          wkid,
          center,
          baseLayers = [],
        },
        cb
      ) {
        const baseLayersInstance = baseLayers?.map((layer) => {
          const layerInstace = createLayer(layer);
          setLayerInstance({ layer: layerInstace, layerName: layer.name });
          return layerInstace;
        });
        const map = new Map({
          ...(baseLayers?.length
            ? { basemap: { baseLayers: baseLayersInstance } }
            : { basemap: null }),
        });
        const view = new MapView({
          map,
          container: this.mapId,
          zoom,
          popupEnabled: false,
          spatialReferenceLocked: true,
          ...(wkid && { spatialReference: { wkid } }),
          constraints: {
            minZoom,
            maxZoom,
            rotationEnabled,
          },
        });
        view.ui.remove("attribution");
        setMapInstance({ map, view, mapId: this.mapId });
        this.setMapState({
          zoom,
          maxZoom,
          minZoom,
          rotationEnabled,
          wkid,
          center,
        });
        cb && cb({ map, view });
        if (center) {
          this.locationMap(center);
        }
      },
      locationMap(center) {
        const { view } = getMapInstance(this.mapId);
        setTimeout((e) => {
          let target = createPointGeometry({
            x: center[0],
            y: center[1],
          });
          view.goTo(
            { target, zoom: 8 },
            {
              duration: 200,
            }
          );
        }, 500);
      },
      destroyMap() {
        const { map, view } = getMapInstance(this.mapId);
        map.destroy();
        view.destroy();
        removeMapInstance(this.mapId);
      },
    },
  });
};
