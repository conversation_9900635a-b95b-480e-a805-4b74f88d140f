<script setup>
import { submitAudit } from "@/api";
import { ElMessage, ElMessageBox } from "element-plus";
import { useZxDataHooks } from "@/components/hooks/zx-data-hooks";

const {
  setupLifecycle,
  handleReset,
  handleSearch,
  handleSizeChange,
  handleCurrentChange,
  handleView,
  handleBack,
  tableState,
  basicCheckItemCommonRef,
  tableRef,
  dynamicSearchRef,
  headerRef,
  rootRef,
  zxDataDetailState,
  formId,
  zxId,
  searchState,
  DynamicSearchForm,
  ZxDataDetail,
  expandRight,
  linkLayerData,
  hightlightGeometryByTz,
  handleMapMounted,
} = useZxDataHooks({ pageType: "lr", mapId: "dynamic-luru" });

setupLifecycle();

const batchSubmit = () => {
  const selectedRows = tableRef.value.getSelectionRows();
  if (selectedRows.length === 0) {
    ElMessage.warning("请选择要提交审核的台账");
    return;
  }
  if (selectedRows.some((item) => item._shzt === null)) {
    ElMessage.warning("所选台账中存在未录入的台账，请先录入");
    return;
  }
  if (selectedRows.some((item) => item._shzt != "999")) {
    ElMessage.warning("所选台账中存在已提交审核的台账，请勿重复提交");
    return;
  }
  ElMessageBox.confirm("确定要提交审核所选台账吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      // 根据_pcid分组
      const pcList = selectedRows.reduce((acc, item) => {
        acc[item._pcid] = acc[item._pcid] || [];
        acc[item._pcid].push(item._rwid);
        return acc;
      }, {});
      const pc = Object.entries(pcList).map(([pcid, rwidList]) => ({
        id: pcid,
        rwid: rwidList,
      }));
      const res = await submitAudit({
        zxid: zxId.value,
        pc,
      });
      if (res.code === 200) {
        ElMessage.success("提交审核成功");
        getTableData();
      } else {
        ElMessage.error(res.message || "提交审核失败");
      }
    })
    .catch(() => {});
};
const handleSubmit = (index, row) => {
  if (row._shzt === null) {
    ElMessage.warning("该台账未录入，请先录入");
    return;
  }
  if (row._shzt != "999") {
    ElMessage.warning("该台账已提交审核，请勿重复提交");
    return;
  }
  ElMessageBox.confirm("确定要提交审核吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await submitAudit({
      zxid: zxId.value,
      pc: [
        {
          id: row._pcid,
          rwid: [row._rwid],
        },
      ],
    });
    if (res.code === 200) {
      ElMessage.success("提交审核成功");
      getTableData();
    } else {
      ElMessage.error(res.message || "提交审核失败");
    }
  });
};
</script>

<template>
  <div
    class="basic-check-item-common flex-left-right"
    ref="rootRef"
    v-if="!zxDataDetailState.visible"
  >
    <div class="basic-check-item-common-left">
      <div class="basic-check-item-common-header" ref="headerRef">
        <div class="basic-check-item-common-header-title">
          <span class="line"></span>
          <span class="title">筛选条件</span>
        </div>

        <DynamicSearchForm
          :jcb-id="formId"
          :zx-id="zxId"
          ref="dynamicSearchRef"
        >
          <template #search-form-item>
            <el-form-item label="批次任务名称">
              <el-select
                v-model="searchState._pcmc"
                placeholder="请选择批次任务名称"
                style="width: 240px"
              >
                <el-option
                  v-for="item in searchState.pcmcList"
                  :key="item.pcmc"
                  :label="item.pcmc"
                  :value="item.pcmc"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select
                v-model="searchState._shzt"
                placeholder="请选择审核状态"
              >
                <el-option
                  v-for="item in searchState.shztList"
                  :key="item.value"
                  :label="item.title"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #search-btn>
            <el-button type="primary" icon="Search" @click="handleSearch"
              >查询</el-button
            >
            <el-button icon="Refresh" @click="handleReset">重置</el-button>
            <el-button icon="Top" type="success" @click="batchSubmit"
              >批量提交审核</el-button
            >
          </template>
        </DynamicSearchForm>
      </div>
      <div
        class="basic-check-item-common-content"
        ref="basicCheckItemCommonRef"
      >
        <div class="basic-check-item-common-header-title">
          <span class="line"></span>
          <span class="title">任务列表 </span>
        </div>
        <div class="basic-check-item-common-content-table">
          <el-table
            :data="tableState.tableData"
            :height="tableState.tableHeight"
            ref="tableRef"
            @row-click="hightlightGeometryByTz"
            tooltip-effect="light"
            :tooltip-options="{
              popperClass: 'fwaq-zx-table-tooltip',
            }"
          >
            <el-table-column type="index" width="55" label="序号" />
            <el-table-column type="selection" width="55" />
            <el-table-column
              v-for="item in tableState.tableColumns"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :width="item.width"
              :min-width="item.minWidth"
              :formatter="item.formatter"
              :show-overflow-tooltip="item.showOverflowTooltip"
            />
            <el-table-column
              label="操作"
              width="200"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  @click="handleView(scope.$index, scope.row)"
                  >查看&录入</el-button
                >
                <el-button
                  size="small"
                  type="success"
                  @click="handleSubmit(scope.$index, scope.row)"
                >
                  提交审核
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="basic-check-item-common-content-pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 30, 40, 50]"
            v-model:current-page="tableState.pageInfo.index"
            v-model:page-size="tableState.pageInfo.size"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :total="tableState.total"
          />
        </div>
      </div>
    </div>
    <div
      class="basic-check-item-common-right"
      :class="expandRight ? 'expand' : 'shrink'"
      v-resize="{ minResize: 30, directions: ['left'] }"
    >
      <ExpandShrink
        @on-collapsed-change="expandRight = !expandRight"
      ></ExpandShrink>
      <GeoSceneMap
        @on-map-mounted="handleMapMounted"
        map-id="dynamic-luru"
        v-if="linkLayerData"
        v-bind="$attrs"
      >
      </GeoSceneMap>
    </div>
  </div>
  <ZxDataDetail
    v-if="zxDataDetailState.visible"
    @back="handleBack"
    :taskStage="'0'"
    :row-data="zxDataDetailState.rowData"
    :zxid="zxId"
  />
</template>
